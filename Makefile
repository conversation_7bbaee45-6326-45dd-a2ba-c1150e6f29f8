.PHONY: build
build:
	cargo build --target wasm32-unknown-unknown --release

.PHONY: protogen
protogen:
	substreams protogen ./substreams.yaml --exclude-paths="sf/substreams,google"

.PHONY: pack
pack: build
	substreams pack ./substreams.yaml

.PHONY: stream
stream: build
	substreams run -e mainnet.sol.streamingfast.io:443 ./substreams.yaml graph_out -s 280000000 -t +1000

.PHONY: gui
gui: build
	substreams gui -e mainnet.sol.streamingfast.io:443 ./substreams.yaml graph_out -s 280000000 -t +1000

.PHONY: test-events
test-events: build
	substreams run -e mainnet.sol.streamingfast.io:443 ./substreams.yaml map_events -s 280000000 -t +100

.PHONY: test-tokens
test-tokens: build
	substreams run -e mainnet.sol.streamingfast.io:443 ./substreams.yaml map_tokens -s 280000000 -t +100

.PHONY: test-trades
test-trades: build
	substreams run -e mainnet.sol.streamingfast.io:443 ./substreams.yaml map_trades -s 280000000 -t +100

.PHONY: clean
clean:
	cargo clean
	rm -rf target/
	rm -f *.spkg

.PHONY: fmt
fmt:
	cargo fmt

.PHONY: clippy
clippy:
	cargo clippy --target wasm32-unknown-unknown

.PHONY: check
check: fmt clippy
	cargo check --target wasm32-unknown-unknown

.PHONY: deploy-studio
deploy-studio: pack
	@echo "To deploy to The Graph Studio, run:"
	@echo "graph deploy --studio <SUBGRAPH_NAME> --deploy-key <DEPLOY_KEY>"

.PHONY: deploy-hosted
deploy-hosted: pack
	@echo "To deploy to hosted service, run:"
	@echo "graph deploy --product hosted-service <GITHUB_USER>/<SUBGRAPH_NAME> --deploy-key <DEPLOY_KEY>"

.PHONY: help
help:
	@echo "Available commands:"
	@echo "  build        - Build the substream WASM binary"
	@echo "  protogen     - Generate protobuf code"
	@echo "  pack         - Pack the substream into .spkg file"
	@echo "  stream       - Run the substream locally"
	@echo "  gui          - Run the substream with GUI"
	@echo "  test-events  - Test event parsing"
	@echo "  test-tokens  - Test token mapping"
	@echo "  test-trades  - Test trade mapping"
	@echo "  clean        - Clean build artifacts"
	@echo "  fmt          - Format code"
	@echo "  clippy       - Run clippy linter"
	@echo "  check        - Run format, clippy and check"
	@echo "  deploy-studio - Show deploy command for Graph Studio"
	@echo "  deploy-hosted - Show deploy command for hosted service"
	@echo "  help         - Show this help message"
