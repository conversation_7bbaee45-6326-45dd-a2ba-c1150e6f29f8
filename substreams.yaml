specVersion: v0.1.0
package:
  name: ibi_cash_amm_substream
  version: v0.1.0

imports:
  entity: https://github.com/streamingfast/substreams-sink-entity-changes/releases/download/v1.3.2/substreams-sink-entity-changes-v1.3.2.spkg
  solana: https://spkg.io/streamingfast/solana-common-v0.3.0.spkg

protobuf:
  files:
    - ibi_cash_amm.proto
  importPaths:
    - ./proto
  excludePaths:
    - sf/substreams/rpc
    - sf/substreams/v1
    - sf/substreams/sink
    - sf/substreams/index
    - sf/substreams/index/v1
    - instructions.proto # sol.instructions.v1 from the v0.2.0 spkg
    - transactions.proto # sol.transactions.v1 from the v0.2.0 spkg
    - google

binaries:
  default:
    type: wasm/rust-v1
    file: ./target/wasm32-unknown-unknown/release/substreams.wasm

modules:
  - name: map_events
    kind: map
    initialBlock: 280000000 # Update this to the block where IBI Cash AMM was deployed
    blockFilter:
      module: solana:program_ids_without_votes
      query:
        string: program:CjytderTMN6nMboF2Unu4HechwR95YRSyW3Td3tvsR6q
    inputs:
      - map: solana:blocks_without_votes
    output:
      type: proto:ibi_cash_amm.v1.Events

  - name: map_global_configs
    kind: map
    initialBlock: 280000000
    inputs:
      - map: map_events
    output:
      type: proto:ibi_cash_amm.v1.GlobalConfigEvent

  - name: store_global_configs
    kind: store
    initialBlock: 280000000
    updatePolicy: set
    valueType: proto:ibi_cash_amm.v1.GlobalConfig
    inputs:
      - map: map_global_configs

  - name: map_tokens
    kind: map
    initialBlock: 280000000
    inputs:
      - map: map_events
      - store: store_global_configs
    output:
      type: proto:ibi_cash_amm.v1.TokenEvent

  - name: store_tokens
    kind: store
    initialBlock: 280000000
    updatePolicy: set
    valueType: proto:ibi_cash_amm.v1.Token
    inputs:
      - map: map_tokens

  - name: map_bonding_curves
    kind: map
    initialBlock: 280000000
    inputs:
      - map: map_events
      - store: store_tokens
    output:
      type: proto:ibi_cash_amm.v1.BondingCurveEvent

  - name: store_bonding_curves
    kind: store
    initialBlock: 280000000
    updatePolicy: set
    valueType: proto:ibi_cash_amm.v1.BondingCurve
    inputs:
      - map: map_bonding_curves

  - name: map_trades
    kind: map
    initialBlock: 280000000
    inputs:
      - map: map_events
      - store: store_tokens
      - store: store_bonding_curves
    output:
      type: proto:ibi_cash_amm.v1.TradeEvent

  - name: store_trades
    kind: store
    initialBlock: 280000000
    updatePolicy: set
    valueType: proto:ibi_cash_amm.v1.Trade
    inputs:
      - map: map_trades

  - name: map_users
    kind: map
    initialBlock: 280000000
    inputs:
      - map: map_trades
    output:
      type: proto:ibi_cash_amm.v1.User

  - name: store_users
    kind: store
    initialBlock: 280000000
    updatePolicy: set
    valueType: proto:ibi_cash_amm.v1.User
    inputs:
      - map: map_users

  - name: map_migrations
    kind: map
    initialBlock: 280000000
    inputs:
      - map: map_events
      - store: store_tokens
      - store: store_bonding_curves
    output:
      type: proto:ibi_cash_amm.v1.MigrationEvent

  - name: store_migrations
    kind: store
    initialBlock: 280000000
    updatePolicy: set
    valueType: proto:ibi_cash_amm.v1.Migration
    inputs:
      - map: map_migrations

  - name: graph_out
    kind: map
    initialBlock: 280000000
    inputs:
      - source: sf.substreams.v1.Clock
      - map: map_global_configs
      - map: map_tokens
      - map: map_bonding_curves
      - map: map_trades
      - map: map_users
      - map: map_migrations
      - store: store_global_configs
      - store: store_tokens
      - store: store_bonding_curves
      - store: store_trades
      - store: store_users
      - store: store_migrations
    output:
      type: proto:sf.substreams.sink.entity.v1.EntityChanges

network: solana-mainnet-beta
