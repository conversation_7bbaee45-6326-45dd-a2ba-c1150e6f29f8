[package]
name = "ibi_cash_amm_substream"
version = "0.1.0"
edition = "2021"

[lib]
name = "substreams"
crate-type = ["cdylib"]

[dependencies]
hex-literal = "0.3.4"
hex = "0.4"
bs58 = "0.5"
borsh = { version = "1.5.0", features = ["derive"] }
num-bigint = "0.4"
num-traits = "0.2.15"
prost = "0.13.3"
prost-types = "0.13.3"
substreams = "0.6.0"
substreams-solana = "0.14.1"
substreams-solana-program-instructions = "0.2.0"
substreams-entity-change = "1.3.2"

# Required so that ethabi > ethereum-types build correctly under wasm32-unknown-unknown
[target.wasm32-unknown-unknown.dependencies]
getrandom = { version = "0.2", features = ["custom"] }

[build-dependencies]

[profile.release]
lto = true
opt-level = 's'
strip = "debuginfo"
