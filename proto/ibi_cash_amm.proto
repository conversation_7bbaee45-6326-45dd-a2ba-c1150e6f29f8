syntax = "proto3";

package ibi_cash_amm.v1;

// ============================================================================
// CORE ENTITIES
// ============================================================================

message GlobalConfig {
  string id = 1;
  bool initialized = 2;
  bytes admin = 3;
  bytes fee_receiver = 4;
  bytes migration_authority = 5;
  string migration_token_allocation = 6;
  string migrate_fee_amount = 7;
  string initial_virtual_token_reserves = 8;
  string initial_virtual_sol_reserves = 9;
  string initial_real_token_reserves = 10;
  string token_total_supply = 11;
  int32 mint_decimals = 12;
  bytes raydium = 13;
  int32 bump = 14;
  string created_at = 15;
  string updated_at = 16;
  bytes transaction_signature = 17;
}

message Token {
  string id = 1;
  bytes mint = 2;
  string name = 3;
  string symbol = 4;
  string uri = 5;
  int32 decimals = 6;
  string total_supply = 7;
  bytes creator = 8;
  string created_at = 9;
  string bonding_curve_id = 10;
  string total_trades = 11;
  string total_volume_sol = 12;
  string total_volume_tokens = 13;
  string total_fees = 14;
  string current_price = 15;
  string market_cap = 16;
  bool migrated = 17;
  string migrated_at = 18;
  bytes migration_transaction = 19;
  bytes raydium_pool_address = 20;
  bytes transaction_signature = 21;
}

message BondingCurve {
  string id = 1;
  bytes mint = 2;
  string token_id = 3;
  bytes creator = 4;
  string global_config_id = 5;
  string initial_real_token_reserves = 6;
  string virtual_sol_reserves = 7;
  string virtual_token_reserves = 8;
  string real_sol_reserves = 9;
  string real_token_reserves = 10;
  string token_total_supply = 11;
  bool started = 12;
  bool complete = 13;
  string progress_percentage = 14;
  int32 bump = 15;
  string created_at = 16;
  string updated_at = 17;
  bytes transaction_signature = 18;
}

message Trade {
  string id = 1;
  string token_id = 2;
  string bonding_curve_id = 3;
  bytes trader = 4;
  TradeType trade_type = 5;
  string sol_amount = 6;
  string token_amount = 7;
  string fee_amount = 8;
  string price_per_token = 9;
  string price_per_sol = 10;
  string virtual_sol_reserves_after = 11;
  string virtual_token_reserves_after = 12;
  string real_sol_reserves_after = 13;
  string real_token_reserves_after = 14;
  bool bonding_curve_complete = 15;
  string progress_percentage_after = 16;
  string slot = 17;
  string block_time = 18;
  bytes transaction_signature = 19;
  int32 instruction_index = 20;
  string created_at = 21;
}

message Migration {
  string id = 1;
  string token_id = 2;
  string bonding_curve_id = 3;
  bytes migration_authority = 4;
  string migration_fee = 5;
  string sol_liquidity = 6;
  string token_liquidity = 7;
  bytes pool_address = 8;
  bytes amm_config = 9;
  string slot = 10;
  string block_time = 11;
  bytes transaction_signature = 12;
  string created_at = 13;
}

message User {
  string id = 1;
  string total_trades = 2;
  string total_volume_sol = 3;
  string total_fees = 4;
  string total_buys = 5;
  string total_sells = 6;
  string total_sol_spent = 7;
  string total_sol_received = 8;
  string total_tokens_bought = 9;
  string total_tokens_sold = 10;
  string realized_pnl_sol = 11;
  string tokens_created = 12;
  string tokens_migrated = 13;
  string first_trade_at = 14;
  string last_trade_at = 15;
  string first_token_created_at = 16;
}

message UserPosition {
  string id = 1;
  string user_id = 2;
  string token_id = 3;
  string token_balance = 4;
  string average_buy_price = 5;
  string total_sol_invested = 6;
  string total_tokens_bought = 7;
  string total_tokens_sold = 8;
  string realized_pnl_sol = 9;
  string unrealized_pnl_sol = 10;
  string total_buys = 11;
  string total_sells = 12;
  string first_buy_at = 13;
  string last_trade_at = 14;
  bool is_active = 15;
}

// ============================================================================
// STATISTICS & ANALYTICS
// ============================================================================

message TokenHourlyStats {
  string id = 1;
  string token_id = 2;
  string period_start_unix = 3;
  string trades = 4;
  string volume_sol = 5;
  string volume_tokens = 6;
  string fees = 7;
  string open = 8;
  string high = 9;
  string low = 10;
  string close = 11;
  string unique_traders = 12;
  string unique_buyers = 13;
  string unique_sellers = 14;
  string progress_start = 15;
  string progress_end = 16;
}

message TokenDailyStats {
  string id = 1;
  string token_id = 2;
  string period_start_unix = 3;
  string trades = 4;
  string volume_sol = 5;
  string volume_tokens = 6;
  string fees = 7;
  string open = 8;
  string high = 9;
  string low = 10;
  string close = 11;
  string unique_traders = 12;
  string unique_buyers = 13;
  string unique_sellers = 14;
  string progress_start = 15;
  string progress_end = 16;
}

message ProtocolDailyStats {
  string id = 1;
  string period_start_unix = 2;
  string tokens_created = 3;
  string tokens_migrated = 4;
  string total_trades = 5;
  string total_volume_sol = 6;
  string total_fees = 7;
  string unique_traders = 8;
  string unique_creators = 9;
  string active_tokens = 10;
}

message BondingCurveSnapshot {
  string id = 1;
  string bonding_curve_id = 2;
  string virtual_sol_reserves = 3;
  string virtual_token_reserves = 4;
  string real_sol_reserves = 5;
  string real_token_reserves = 6;
  string progress_percentage = 7;
  bool complete = 8;
  string price_per_token = 9;
  string slot = 10;
  string block_time = 11;
  bytes transaction_signature = 12;
}

// ============================================================================
// ENUMS
// ============================================================================

enum TradeType {
  TRADE_TYPE_UNSPECIFIED = 0;
  BUY = 1;
  SELL = 2;
}

// ============================================================================
// EVENT COLLECTIONS
// ============================================================================

message Events {
  repeated GlobalConfigEvent global_config_events = 1;
  repeated TokenEvent token_events = 2;
  repeated BondingCurveEvent bonding_curve_events = 3;
  repeated TradeEvent trade_events = 4;
  repeated MigrationEvent migration_events = 5;
}

message GlobalConfigEvent {
  GlobalConfig global_config = 1;
  string event_type = 2;
  string block_time = 3;
  string slot = 4;
  bytes transaction_signature = 5;
}

message TokenEvent {
  Token token = 1;
  string event_type = 2;
  string block_time = 3;
  string slot = 4;
  bytes transaction_signature = 5;
}

message BondingCurveEvent {
  BondingCurve bonding_curve = 1;
  string event_type = 2;
  string block_time = 3;
  string slot = 4;
  bytes transaction_signature = 5;
}

message TradeEvent {
  Trade trade = 1;
  string event_type = 2;
  string block_time = 3;
  string slot = 4;
  bytes transaction_signature = 5;
}

message MigrationEvent {
  Migration migration = 1;
  string event_type = 2;
  string block_time = 3;
  string slot = 4;
  bytes transaction_signature = 5;
}
