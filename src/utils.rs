use std::str::FromStr;

/// Calculate bonding curve price based on virtual reserves
pub fn calculate_price(virtual_sol_reserves: u64, virtual_token_reserves: u64) -> f64 {
    if virtual_token_reserves == 0 {
        return 0.0;
    }
    virtual_sol_reserves as f64 / virtual_token_reserves as f64
}

/// Calculate progress percentage of bonding curve
pub fn calculate_progress_percentage(real_sol_reserves: u64, target_sol_reserves: u64) -> f64 {
    if target_sol_reserves == 0 {
        return 0.0;
    }
    (real_sol_reserves as f64 / target_sol_reserves as f64) * 100.0
}

/// Calculate market cap based on current price and circulating supply
pub fn calculate_market_cap(current_price: f64, circulating_supply: u64) -> f64 {
    current_price * circulating_supply as f64
}

/// Calculate token amount out for a given SOL amount in (buy)
pub fn calculate_token_amount_out(
    sol_amount_in: u64,
    virtual_sol_reserves: u64,
    virtual_token_reserves: u64,
) -> u64 {
    if virtual_sol_reserves == 0 || virtual_token_reserves == 0 {
        return 0;
    }

    // Using constant product formula: x * y = k
    // token_out = virtual_token_reserves - (virtual_sol_reserves * virtual_token_reserves) / (virtual_sol_reserves + sol_amount_in)
    let numerator = (virtual_sol_reserves as u128) * (virtual_token_reserves as u128);
    let denominator = (virtual_sol_reserves as u128) + (sol_amount_in as u128);

    if denominator == 0 {
        return 0;
    }

    let new_token_reserves = numerator / denominator;
    if new_token_reserves >= virtual_token_reserves as u128 {
        return 0;
    }

    (virtual_token_reserves as u128 - new_token_reserves) as u64
}

/// Calculate SOL amount out for a given token amount in (sell)
pub fn calculate_sol_amount_out(
    token_amount_in: u64,
    virtual_sol_reserves: u64,
    virtual_token_reserves: u64,
) -> u64 {
    if virtual_sol_reserves == 0 || virtual_token_reserves == 0 {
        return 0;
    }

    // Using constant product formula: x * y = k
    // sol_out = virtual_sol_reserves - (virtual_sol_reserves * virtual_token_reserves) / (virtual_token_reserves + token_amount_in)
    let numerator = (virtual_sol_reserves as u128) * (virtual_token_reserves as u128);
    let denominator = (virtual_token_reserves as u128) + (token_amount_in as u128);

    if denominator == 0 {
        return 0;
    }

    let new_sol_reserves = numerator / denominator;
    if new_sol_reserves >= virtual_sol_reserves as u128 {
        return 0;
    }

    (virtual_sol_reserves as u128 - new_sol_reserves) as u64
}

/// Calculate trading fee (typically 1% of the trade amount)
pub fn calculate_trading_fee(amount: u64, fee_bps: u16) -> u64 {
    (amount as u128 * fee_bps as u128 / 10000) as u64
}

/// Convert timestamp to hour bucket for aggregations
pub fn timestamp_to_hour_bucket(timestamp: i64) -> i64 {
    timestamp / 3600 * 3600
}

/// Convert timestamp to day bucket for aggregations
pub fn timestamp_to_day_bucket(timestamp: i64) -> i64 {
    timestamp / 86400 * 86400
}

/// Format public key as string
pub fn pubkey_to_string(pubkey: &[u8]) -> String {
    if pubkey.len() == 32 {
        bs58::encode(pubkey).into_string()
    } else {
        hex::encode(pubkey)
    }
}

/// Parse string to u64, returning 0 if parsing fails
pub fn parse_u64_or_zero(s: &str) -> u64 {
    u64::from_str(s).unwrap_or(0)
}

/// Parse string to f64, returning 0.0 if parsing fails
pub fn parse_f64_or_zero(s: &str) -> f64 {
    f64::from_str(s).unwrap_or(0.0)
}

/// Convert lamports to SOL (divide by 10^9)
pub fn lamports_to_sol(lamports: u64) -> f64 {
    lamports as f64 / 1_000_000_000.0
}

/// Convert SOL to lamports (multiply by 10^9)
pub fn sol_to_lamports(sol: f64) -> u64 {
    (sol * 1_000_000_000.0) as u64
}

/// Convert token amount based on decimals
pub fn convert_token_amount(amount: u64, decimals: u8) -> f64 {
    amount as f64 / 10_u64.pow(decimals as u32) as f64
}

/// Check if bonding curve is complete based on progress
pub fn is_bonding_curve_complete(progress_percentage: f64) -> bool {
    progress_percentage >= 100.0
}

/// Generate unique ID for entities
pub fn generate_entity_id(prefix: &str, components: &[&str]) -> String {
    format!("{}_{}", prefix, components.join("_"))
}

/// Calculate average price from multiple trades
pub fn calculate_average_price(prices: &[f64], volumes: &[f64]) -> f64 {
    if prices.is_empty() || volumes.is_empty() || prices.len() != volumes.len() {
        return 0.0;
    }

    let total_volume: f64 = volumes.iter().sum();
    if total_volume == 0.0 {
        return 0.0;
    }

    let weighted_sum: f64 = prices
        .iter()
        .zip(volumes.iter())
        .map(|(price, volume)| price * volume)
        .sum();

    weighted_sum / total_volume
}

/// Calculate percentage change
pub fn calculate_percentage_change(old_value: f64, new_value: f64) -> f64 {
    if old_value == 0.0 {
        return 0.0;
    }
    ((new_value - old_value) / old_value) * 100.0
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_calculate_price() {
        assert_eq!(calculate_price(1000, 2000), 0.5);
        assert_eq!(calculate_price(0, 1000), 0.0);
        assert_eq!(calculate_price(1000, 0), 0.0);
    }

    #[test]
    fn test_calculate_progress_percentage() {
        assert_eq!(calculate_progress_percentage(50, 100), 50.0);
        assert_eq!(calculate_progress_percentage(100, 100), 100.0);
        assert_eq!(calculate_progress_percentage(0, 100), 0.0);
    }

    #[test]
    fn test_calculate_token_amount_out() {
        let result = calculate_token_amount_out(1000, 10000, 20000);
        assert!(result > 0);
    }

    #[test]
    fn test_timestamp_buckets() {
        let timestamp = 1640995200; // 2022-01-01 00:00:00
        assert_eq!(timestamp_to_hour_bucket(timestamp + 1800), timestamp); // +30 minutes
        assert_eq!(timestamp_to_day_bucket(timestamp + 43200), timestamp); // +12 hours
    }

    #[test]
    fn test_lamports_conversion() {
        assert_eq!(lamports_to_sol(1_000_000_000), 1.0);
        assert_eq!(sol_to_lamports(1.0), 1_000_000_000);
    }
}
