use crate::pb::ibi_cash_amm::v1 as ibi_cash_amm;
use crate::utils;

/// Calculate updated bonding curve state after a trade
pub fn calculate_bonding_curve_after_trade(
    bonding_curve: &ibi_cash_amm::BondingCurve,
    trade: &ibi_cash_amm::Trade,
) -> ibi_cash_amm::BondingCurve {
    let mut updated_curve = bonding_curve.clone();

    let virtual_sol_reserves = utils::parse_u64_or_zero(&bonding_curve.virtual_sol_reserves);
    let virtual_token_reserves = utils::parse_u64_or_zero(&bonding_curve.virtual_token_reserves);
    let real_sol_reserves = utils::parse_u64_or_zero(&bonding_curve.real_sol_reserves);
    let real_token_reserves = utils::parse_u64_or_zero(&bonding_curve.real_token_reserves);

    let sol_amount = utils::parse_u64_or_zero(&trade.sol_amount);
    let token_amount = utils::parse_u64_or_zero(&trade.token_amount);

    let (new_virtual_sol, new_virtual_token, new_real_sol, new_real_token) =
        if trade.trade_type == ibi_cash_amm::TradeType::Buy as i32 {
            // Buy: SOL in, tokens out
            (
                virtual_sol_reserves + sol_amount,
                virtual_token_reserves - token_amount,
                real_sol_reserves + sol_amount,
                real_token_reserves - token_amount,
            )
        } else {
            // Sell: tokens in, SOL out
            (
                virtual_sol_reserves - sol_amount,
                virtual_token_reserves + token_amount,
                real_sol_reserves - sol_amount,
                real_token_reserves + token_amount,
            )
        };

    // Calculate progress percentage
    let target_sol_reserves = 85_000_000_000u64; // 85 SOL target for migration
    let progress = utils::calculate_progress_percentage(new_real_sol, target_sol_reserves);
    let is_complete = utils::is_bonding_curve_complete(progress);

    updated_curve.virtual_sol_reserves = new_virtual_sol.to_string();
    updated_curve.virtual_token_reserves = new_virtual_token.to_string();
    updated_curve.real_sol_reserves = new_real_sol.to_string();
    updated_curve.real_token_reserves = new_real_token.to_string();
    updated_curve.progress_percentage = progress.to_string();
    updated_curve.complete = is_complete;
    updated_curve.updated_at = trade.block_time.clone();

    updated_curve
}

/// Calculate trade amounts and prices
pub fn calculate_trade_details(
    trade_args: &crate::instructions::SwapArgs,
    bonding_curve: &ibi_cash_amm::BondingCurve,
) -> (u64, u64, u64, f64, f64) {
    let virtual_sol_reserves = utils::parse_u64_or_zero(&bonding_curve.virtual_sol_reserves);
    let virtual_token_reserves = utils::parse_u64_or_zero(&bonding_curve.virtual_token_reserves);

    let (sol_amount, token_amount, fee_amount) = if trade_args.direction == 0 {
        // Buy: calculate tokens out for SOL in
        let fee = utils::calculate_trading_fee(trade_args.amount, 100); // 1% fee
        let sol_after_fee = trade_args.amount - fee;
        let tokens_out = utils::calculate_token_amount_out(
            sol_after_fee,
            virtual_sol_reserves,
            virtual_token_reserves,
        );
        (trade_args.amount, tokens_out, fee)
    } else {
        // Sell: calculate SOL out for tokens in
        let sol_out = utils::calculate_sol_amount_out(
            trade_args.amount,
            virtual_sol_reserves,
            virtual_token_reserves,
        );
        let fee = utils::calculate_trading_fee(sol_out, 100); // 1% fee
        let sol_after_fee = sol_out - fee;
        (sol_after_fee, trade_args.amount, fee)
    };

    // Calculate prices
    let price_per_token = if token_amount > 0 {
        utils::lamports_to_sol(sol_amount) / utils::convert_token_amount(token_amount, 6)
    } else {
        0.0
    };

    let price_per_sol = if sol_amount > 0 {
        utils::convert_token_amount(token_amount, 6) / utils::lamports_to_sol(sol_amount)
    } else {
        0.0
    };

    (
        sol_amount,
        token_amount,
        fee_amount,
        price_per_token,
        price_per_sol,
    )
}

/// Calculate token statistics
pub fn calculate_token_stats(
    token: &ibi_cash_amm::Token,
    trades: &[ibi_cash_amm::Trade],
) -> ibi_cash_amm::Token {
    let mut updated_token = token.clone();

    let total_trades = trades.len() as u64;
    let mut total_volume_sol = 0u64;
    let mut total_volume_tokens = 0u64;
    let mut total_fees = 0u64;
    let mut prices = Vec::new();

    for trade in trades {
        total_volume_sol += utils::parse_u64_or_zero(&trade.sol_amount);
        total_volume_tokens += utils::parse_u64_or_zero(&trade.token_amount);
        total_fees += utils::parse_u64_or_zero(&trade.fee_amount);
        prices.push(utils::parse_f64_or_zero(&trade.price_per_token));
    }

    // Calculate current price (last trade price or average)
    let current_price = prices.last().copied().unwrap_or(0.0);

    // Calculate market cap
    let total_supply = utils::parse_u64_or_zero(&token.total_supply);
    let market_cap = utils::calculate_market_cap(current_price, total_supply);

    updated_token.total_trades = total_trades.to_string();
    updated_token.total_volume_sol = utils::lamports_to_sol(total_volume_sol).to_string();
    updated_token.total_volume_tokens =
        utils::convert_token_amount(total_volume_tokens, 6).to_string();
    updated_token.total_fees = utils::lamports_to_sol(total_fees).to_string();
    updated_token.current_price = current_price.to_string();
    updated_token.market_cap = market_cap.to_string();

    updated_token
}

/// Calculate user position for a specific token
pub fn calculate_user_position(
    user_id: &str,
    token_id: &str,
    trades: &[ibi_cash_amm::Trade],
) -> ibi_cash_amm::UserPosition {
    let mut total_tokens_bought = 0u64;
    let mut total_tokens_sold = 0u64;
    let mut total_sol_invested = 0u64;
    let mut total_sol_received = 0u64;
    let mut total_buys = 0u64;
    let mut total_sells = 0u64;
    let mut first_buy_at = "0".to_string();
    let mut last_trade_at = "0".to_string();
    let mut buy_prices = Vec::new();
    let mut buy_amounts = Vec::new();

    for trade in trades {
        if utils::pubkey_to_string(&trade.trader) == user_id && trade.token_id == token_id {
            last_trade_at = trade.block_time.clone();

            if trade.trade_type == ibi_cash_amm::TradeType::Buy as i32 {
                let token_amount = utils::parse_u64_or_zero(&trade.token_amount);
                let sol_amount = utils::parse_u64_or_zero(&trade.sol_amount);
                let price = utils::parse_f64_or_zero(&trade.price_per_token);

                total_tokens_bought += token_amount;
                total_sol_invested += sol_amount;
                total_buys += 1;

                if first_buy_at == "0" {
                    first_buy_at = trade.block_time.clone();
                }

                buy_prices.push(price);
                buy_amounts.push(token_amount as f64);
            } else {
                let token_amount = utils::parse_u64_or_zero(&trade.token_amount);
                let sol_amount = utils::parse_u64_or_zero(&trade.sol_amount);

                total_tokens_sold += token_amount;
                total_sol_received += sol_amount;
                total_sells += 1;
            }
        }
    }

    // Calculate current token balance
    let token_balance = if total_tokens_bought > total_tokens_sold {
        total_tokens_bought - total_tokens_sold
    } else {
        0
    };

    // Calculate average buy price
    let average_buy_price = if !buy_prices.is_empty() {
        utils::calculate_average_price(&buy_prices, &buy_amounts)
    } else {
        0.0
    };

    // Calculate realized P&L
    let realized_pnl_sol = if total_sol_received > 0 {
        utils::lamports_to_sol(total_sol_received) - utils::lamports_to_sol(total_sol_invested)
    } else {
        0.0
    };

    let position_id = utils::generate_entity_id("position", &[user_id, token_id]);

    ibi_cash_amm::UserPosition {
        id: position_id,
        user_id: user_id.to_string(),
        token_id: token_id.to_string(),
        token_balance: utils::convert_token_amount(token_balance, 6).to_string(),
        average_buy_price: average_buy_price.to_string(),
        total_sol_invested: utils::lamports_to_sol(total_sol_invested).to_string(),
        total_tokens_bought: utils::convert_token_amount(total_tokens_bought, 6).to_string(),
        total_tokens_sold: utils::convert_token_amount(total_tokens_sold, 6).to_string(),
        realized_pnl_sol: realized_pnl_sol.to_string(),
        unrealized_pnl_sol: "0".to_string(), // Would need current price to calculate
        total_buys: total_buys.to_string(),
        total_sells: total_sells.to_string(),
        first_buy_at,
        last_trade_at,
        is_active: token_balance > 0,
    }
}

/// Calculate hourly statistics for a token
pub fn calculate_hourly_stats(
    token_id: &str,
    hour_timestamp: i64,
    trades: &[ibi_cash_amm::Trade],
) -> ibi_cash_amm::TokenHourlyStats {
    let hour_start = utils::timestamp_to_hour_bucket(hour_timestamp);
    let hour_end = hour_start + 3600;

    let hour_trades: Vec<_> = trades
        .iter()
        .filter(|trade| {
            let trade_time = utils::parse_u64_or_zero(&trade.block_time) as i64;
            trade.token_id == token_id && trade_time >= hour_start && trade_time < hour_end
        })
        .collect();

    if hour_trades.is_empty() {
        return ibi_cash_amm::TokenHourlyStats {
            id: utils::generate_entity_id("hourly", &[token_id, &hour_start.to_string()]),
            token_id: token_id.to_string(),
            period_start_unix: hour_start.to_string(),
            trades: "0".to_string(),
            volume_sol: "0".to_string(),
            volume_tokens: "0".to_string(),
            fees: "0".to_string(),
            open: "0".to_string(),
            high: "0".to_string(),
            low: "0".to_string(),
            close: "0".to_string(),
            unique_traders: "0".to_string(),
            unique_buyers: "0".to_string(),
            unique_sellers: "0".to_string(),
            progress_start: "0".to_string(),
            progress_end: "0".to_string(),
        };
    }

    let mut volume_sol = 0u64;
    let mut volume_tokens = 0u64;
    let mut fees = 0u64;
    let mut prices = Vec::new();
    let mut unique_traders = std::collections::HashSet::new();
    let mut unique_buyers = std::collections::HashSet::new();
    let mut unique_sellers = std::collections::HashSet::new();

    for trade in &hour_trades {
        volume_sol += utils::parse_u64_or_zero(&trade.sol_amount);
        volume_tokens += utils::parse_u64_or_zero(&trade.token_amount);
        fees += utils::parse_u64_or_zero(&trade.fee_amount);
        prices.push(utils::parse_f64_or_zero(&trade.price_per_token));

        let trader = utils::pubkey_to_string(&trade.trader);
        unique_traders.insert(trader.clone());

        if trade.trade_type == ibi_cash_amm::TradeType::Buy as i32 {
            unique_buyers.insert(trader);
        } else {
            unique_sellers.insert(trader);
        }
    }

    let open = prices.first().copied().unwrap_or(0.0);
    let close = prices.last().copied().unwrap_or(0.0);
    let high = prices.iter().fold(0.0f64, |a, &b| a.max(b));
    let low = prices.iter().fold(f64::INFINITY, |a, &b| a.min(b));

    ibi_cash_amm::TokenHourlyStats {
        id: utils::generate_entity_id("hourly", &[token_id, &hour_start.to_string()]),
        token_id: token_id.to_string(),
        period_start_unix: hour_start.to_string(),
        trades: hour_trades.len().to_string(),
        volume_sol: utils::lamports_to_sol(volume_sol).to_string(),
        volume_tokens: utils::convert_token_amount(volume_tokens, 6).to_string(),
        fees: utils::lamports_to_sol(fees).to_string(),
        open: open.to_string(),
        high: high.to_string(),
        low: if low == f64::INFINITY {
            "0".to_string()
        } else {
            low.to_string()
        },
        close: close.to_string(),
        unique_traders: unique_traders.len().to_string(),
        unique_buyers: unique_buyers.len().to_string(),
        unique_sellers: unique_sellers.len().to_string(),
        progress_start: hour_trades
            .first()
            .map(|t| t.progress_percentage_after.clone())
            .unwrap_or("0".to_string()),
        progress_end: hour_trades
            .last()
            .map(|t| t.progress_percentage_after.clone())
            .unwrap_or("0".to_string()),
    }
}
