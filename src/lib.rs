mod calculations;
mod instructions;
mod pb;
mod utils;

use pb::ibi_cash_amm::v1 as ibi_cash_amm;
use substreams::errors::Error;
use substreams::pb::substreams::Clock;
use substreams_entity_change::pb::entity::EntityChanges;
use substreams_entity_change::tables::Tables;
use substreams_solana::b58;
use substreams_solana::pb::sf::solana::r#type::v1::Block;

// IBI Cash AMM Program ID
const IBI_CASH_AMM_PROGRAM_ID: [u8; 32] = b58!("CjytderTMN6nMboF2Unu4HechwR95YRSyW3Td3tvsR6q");

#[substreams::handlers::map]
fn map_events(blk: Block) -> Result<ibi_cash_amm::Events, Error> {
    let mut events = ibi_cash_amm::Events::default();

    for transaction in blk.transactions() {
        if let Some(meta) = &transaction.meta {
            if meta.err.is_some() {
                continue; // Skip failed transactions
            }
        }

        for (instruction_index, instruction) in transaction.walk_instructions().enumerate() {
            if instruction.program_id() != IBI_CASH_AMM_PROGRAM_ID {
                continue;
            }

            match instructions::parse_instruction(&instruction, &blk, instruction_index as u32) {
                Ok(parsed_events) => {
                    events
                        .global_config_events
                        .extend(parsed_events.global_config_events);
                    events.token_events.extend(parsed_events.token_events);
                    events
                        .bonding_curve_events
                        .extend(parsed_events.bonding_curve_events);
                    events.trade_events.extend(parsed_events.trade_events);
                    events
                        .migration_events
                        .extend(parsed_events.migration_events);
                }
                Err(e) => {
                    substreams::log::info!("Failed to parse instruction: {}", e);
                }
            }
        }
    }

    Ok(events)
}

#[substreams::handlers::map]
fn map_global_configs(
    events: ibi_cash_amm::Events,
) -> Result<ibi_cash_amm::GlobalConfigEvent, Error> {
    for event in events.global_config_events {
        return Ok(event);
    }
    // Err(Error::Unexpected(
    //     "No global config events found".to_string(),
    // ))
    Err(Error::msg("No global config events found"))
}

#[substreams::handlers::map]
fn map_tokens(events: ibi_cash_amm::Events) -> Result<ibi_cash_amm::TokenEvent, Error> {
    for event in events.token_events {
        return Ok(event);
    }
    // Err(Error::Unexpected("No token events found".to_string()))
    Err(Error::msg("No token events found"))
}

#[substreams::handlers::map]
fn map_bonding_curves(
    events: ibi_cash_amm::Events,
) -> Result<ibi_cash_amm::BondingCurveEvent, Error> {
    for event in events.bonding_curve_events {
        return Ok(event);
    }
    // Err(Error::Unexpected(
    //     "No bonding curve events found".to_string(),
    // ))
    Err(Error::msg("No bonding curve events found"))
}

#[substreams::handlers::map]
fn map_trades(events: ibi_cash_amm::Events) -> Result<ibi_cash_amm::TradeEvent, Error> {
    for event in events.trade_events {
        return Ok(event);
    }
    // Err(Error::Unexpected("No trade events found".to_string()))
    Err(Error::msg("No trade events found"))
}

#[substreams::handlers::map]
fn map_users(trade_events: ibi_cash_amm::TradeEvent) -> Result<ibi_cash_amm::User, Error> {
    if let Some(trade) = trade_events.trade {
        let user_id = utils::pubkey_to_string(&trade.trader);

        let user = ibi_cash_amm::User {
            id: user_id,
            total_trades: "1".to_string(),
            total_volume_sol: trade.sol_amount.clone().clone(),
            total_fees: trade.fee_amount,
            total_buys: if trade.trade_type == ibi_cash_amm::TradeType::Buy as i32 {
                "1".to_string()
            } else {
                "0".to_string()
            },
            total_sells: if trade.trade_type == ibi_cash_amm::TradeType::Sell as i32 {
                "1".to_string()
            } else {
                "0".to_string()
            },
            total_sol_spent: if trade.trade_type == ibi_cash_amm::TradeType::Buy as i32 {
                trade.sol_amount.clone()
            } else {
                "0".to_string()
            },
            total_sol_received: if trade.trade_type == ibi_cash_amm::TradeType::Sell as i32 {
                trade.sol_amount.clone()
            } else {
                "0".to_string()
            },
            total_tokens_bought: if trade.trade_type == ibi_cash_amm::TradeType::Buy as i32 {
                trade.token_amount.clone()
            } else {
                "0".to_string()
            },
            total_tokens_sold: if trade.trade_type == ibi_cash_amm::TradeType::Sell as i32 {
                trade.token_amount.clone()
            } else {
                "0".to_string()
            },
            realized_pnl_sol: "0".to_string(),
            tokens_created: "0".to_string(),
            tokens_migrated: "0".to_string(),
            first_trade_at: trade.block_time.clone(),
            last_trade_at: trade.block_time,
            first_token_created_at: "0".to_string(),
        };

        return Ok(user);
    }

    // Err(Error::Unexpected(
    //     "No trade found in trade event".to_string(),
    // ))
    Err(Error::msg("No trade found in trade event"))
}

#[substreams::handlers::map]
fn map_migrations(events: ibi_cash_amm::Events) -> Result<ibi_cash_amm::MigrationEvent, Error> {
    for event in events.migration_events {
        return Ok(event);
    }
    // Err(Error::Unexpected("No migration events found".to_string()))
    Err(Error::msg("No migration events found"))
}

#[substreams::handlers::map]
fn graph_out(
    clock: Clock,
    global_config_events: Option<ibi_cash_amm::GlobalConfigEvent>,
    token_events: Option<ibi_cash_amm::TokenEvent>,
    bonding_curve_events: Option<ibi_cash_amm::BondingCurveEvent>,
    trade_events: Option<ibi_cash_amm::TradeEvent>,
    user_events: Option<ibi_cash_amm::User>,
    migration_events: Option<ibi_cash_amm::MigrationEvent>,
) -> Result<EntityChanges, Error> {
    let mut tables = Tables::new();

    // Process GlobalConfig events
    if let Some(event) = global_config_events {
        if let Some(global_config) = event.global_config {
            tables
                .create_row("GlobalConfig", &global_config.id)
                .set("initialized", global_config.initialized)
                .set("admin", hex::encode(&global_config.admin))
                .set("feeReceiver", hex::encode(&global_config.fee_receiver))
                .set(
                    "migrationAuthority",
                    hex::encode(&global_config.migration_authority),
                )
                .set(
                    "migrationTokenAllocation",
                    &global_config.migration_token_allocation,
                )
                .set("migrateFeeAmount", &global_config.migrate_fee_amount)
                .set(
                    "initialVirtualTokenReserves",
                    &global_config.initial_virtual_token_reserves,
                )
                .set(
                    "initialVirtualSolReserves",
                    &global_config.initial_virtual_sol_reserves,
                )
                .set(
                    "initialRealTokenReserves",
                    &global_config.initial_real_token_reserves,
                )
                .set("tokenTotalSupply", &global_config.token_total_supply)
                .set("mintDecimals", global_config.mint_decimals)
                .set("raydium", hex::encode(&global_config.raydium))
                .set("bump", global_config.bump)
                .set("createdAt", &global_config.created_at)
                .set("updatedAt", &global_config.updated_at)
                .set(
                    "transactionSignature",
                    hex::encode(&global_config.transaction_signature),
                );
        }
    }

    // Process Token events
    if let Some(event) = token_events {
        if let Some(token) = event.token {
            tables
                .create_row("Token", &token.id)
                .set("mint", hex::encode(&token.mint))
                .set("name", &token.name)
                .set("symbol", &token.symbol)
                .set("uri", &token.uri)
                .set("decimals", token.decimals)
                .set("totalSupply", &token.total_supply)
                .set("creator", hex::encode(&token.creator))
                .set("createdAt", &token.created_at)
                .set("bondingCurve", &token.bonding_curve_id)
                .set("totalTrades", &token.total_trades)
                .set("totalVolumeSol", &token.total_volume_sol)
                .set("totalVolumeTokens", &token.total_volume_tokens)
                .set("totalFees", &token.total_fees)
                .set("currentPrice", &token.current_price)
                .set("marketCap", &token.market_cap)
                .set("migrated", token.migrated)
                .set("migratedAt", &token.migrated_at)
                .set(
                    "migrationTransaction",
                    hex::encode(&token.migration_transaction),
                )
                .set(
                    "raydiumPoolAddress",
                    hex::encode(&token.raydium_pool_address),
                )
                .set(
                    "transactionSignature",
                    hex::encode(&token.transaction_signature),
                );
        }
    }

    // Process BondingCurve events
    if let Some(event) = bonding_curve_events {
        if let Some(bonding_curve) = event.bonding_curve {
            tables
                .create_row("BondingCurve", &bonding_curve.id)
                .set("mint", hex::encode(&bonding_curve.mint))
                .set("token", &bonding_curve.token_id)
                .set("creator", hex::encode(&bonding_curve.creator))
                .set("globalConfig", &bonding_curve.global_config_id)
                .set(
                    "initialRealTokenReserves",
                    &bonding_curve.initial_real_token_reserves,
                )
                .set("virtualSolReserves", &bonding_curve.virtual_sol_reserves)
                .set(
                    "virtualTokenReserves",
                    &bonding_curve.virtual_token_reserves,
                )
                .set("realSolReserves", &bonding_curve.real_sol_reserves)
                .set("realTokenReserves", &bonding_curve.real_token_reserves)
                .set("tokenTotalSupply", &bonding_curve.token_total_supply)
                .set("started", bonding_curve.started)
                .set("complete", bonding_curve.complete)
                .set("progressPercentage", &bonding_curve.progress_percentage)
                .set("bump", bonding_curve.bump)
                .set("createdAt", &bonding_curve.created_at)
                .set("updatedAt", &bonding_curve.updated_at)
                .set(
                    "transactionSignature",
                    hex::encode(&bonding_curve.transaction_signature),
                );
        }
    }

    // Process Trade events
    if let Some(event) = trade_events {
        if let Some(trade) = event.trade {
            let trade_type_str = match trade.trade_type {
                1 => "BUY",
                2 => "SELL",
                _ => "UNKNOWN",
            };

            tables
                .create_row("Trade", &trade.id)
                .set("token", &trade.token_id)
                .set("bondingCurve", &trade.bonding_curve_id)
                .set("trader", hex::encode(&trade.trader))
                .set("tradeType", trade_type_str)
                .set("solAmount", &trade.sol_amount)
                .set("tokenAmount", &trade.token_amount)
                .set("feeAmount", &trade.fee_amount)
                .set("pricePerToken", &trade.price_per_token)
                .set("pricePerSol", &trade.price_per_sol)
                .set("virtualSolReservesAfter", &trade.virtual_sol_reserves_after)
                .set(
                    "virtualTokenReservesAfter",
                    &trade.virtual_token_reserves_after,
                )
                .set("realSolReservesAfter", &trade.real_sol_reserves_after)
                .set("realTokenReservesAfter", &trade.real_token_reserves_after)
                .set("bondingCurveComplete", trade.bonding_curve_complete)
                .set("progressPercentageAfter", &trade.progress_percentage_after)
                .set("slot", &trade.slot)
                .set("blockTime", &trade.block_time)
                .set(
                    "transactionSignature",
                    hex::encode(&trade.transaction_signature),
                )
                .set("instructionIndex", trade.instruction_index)
                .set("createdAt", &trade.created_at);
        }
    }

    // Process User events
    if let Some(user) = user_events {
        tables
            .create_row("User", &user.id)
            .set("totalTrades", &user.total_trades)
            .set("totalVolumeSol", &user.total_volume_sol)
            .set("totalFees", &user.total_fees)
            .set("totalBuys", &user.total_buys)
            .set("totalSells", &user.total_sells)
            .set("totalSolSpent", &user.total_sol_spent)
            .set("totalSolReceived", &user.total_sol_received)
            .set("totalTokensBought", &user.total_tokens_bought)
            .set("totalTokensSold", &user.total_tokens_sold)
            .set("realizedPnlSol", &user.realized_pnl_sol)
            .set("tokensCreated", &user.tokens_created)
            .set("tokensMigrated", &user.tokens_migrated)
            .set("firstTradeAt", &user.first_trade_at)
            .set("lastTradeAt", &user.last_trade_at)
            .set("firstTokenCreatedAt", &user.first_token_created_at);
    }

    // Process Migration events
    if let Some(event) = migration_events {
        if let Some(migration) = event.migration {
            tables
                .create_row("Migration", &migration.id)
                .set("token", &migration.token_id)
                .set("bondingCurve", &migration.bonding_curve_id)
                .set(
                    "migrationAuthority",
                    hex::encode(&migration.migration_authority),
                )
                .set("migrationFee", &migration.migration_fee)
                .set("solLiquidity", &migration.sol_liquidity)
                .set("tokenLiquidity", &migration.token_liquidity)
                .set("poolAddress", hex::encode(&migration.pool_address))
                .set("ammConfig", hex::encode(&migration.amm_config))
                .set("slot", &migration.slot)
                .set("blockTime", &migration.block_time)
                .set(
                    "transactionSignature",
                    hex::encode(&migration.transaction_signature),
                )
                .set("createdAt", &migration.created_at);
        }
    }

    Ok(tables.to_entity_changes())
}
