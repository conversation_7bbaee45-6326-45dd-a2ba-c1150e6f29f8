# IBI Cash AMM Substream

Este projeto implementa um substream para indexar dados do protocolo IBI Cash AMM na Solana usando Substreams e The Graph Protocol.

## 📋 Visão Geral

O IBI Cash AMM é um protocolo de bonding curve na Solana que permite:
- **Criação de tokens** com bonding curves automáticas
- **Trading** (compra/venda) através das bonding curves
- **Migração** automática para Raydium quando a bonding curve completa
- **Estatísticas** e analytics em tempo real

**Program ID**: `CjytderTMN6nMboF2Unu4HechwR95YRSyW3Td3tvsR6q`

## 🏗️ Arquitetura

### Entidades Principais
- **GlobalConfig**: Configuração global do programa
- **Token**: Tokens criados no protocolo
- **BondingCurve**: Estado das bonding curves
- **Trade**: Transações de compra/venda
- **Migration**: Migrações para Raydium
- **User**: Estatísticas de usuários
- **UserPosition**: Posições específicas por token

### Módulos Substream
1. **map_events**: Extrai eventos das instruções do programa
2. **map_tokens**: Processa criação de tokens
3. **map_bonding_curves**: Processa estado das bonding curves
4. **map_trades**: Processa transações de trading
5. **map_users**: Agrega estatísticas de usuários
6. **map_migrations**: Processa migrações para Raydium
7. **graph_out**: Gera EntityChanges para The Graph

## 🚀 Getting Started

### Pré-requisitos
- [Rust](https://rustup.rs/) (latest stable)
- [Substreams CLI](https://substreams.streamingfast.io/getting-started/installing-the-cli)
- [Graph CLI](https://thegraph.com/docs/en/cookbook/quick-start/) (para deploy)

### Instalação
```bash
# Clone o repositório
git clone <repository-url>
cd solana-subgraph

# Instale dependências
cargo build --target wasm32-unknown-unknown --release
```

### Build e Deploy

#### 1. Gerar código protobuf
```bash
make protogen
```

#### 2. Build do substream
```bash
make build
```

#### 3. Testar localmente
```bash
# Testar parsing de eventos
make test-events

# Testar mapeamento de tokens
make test-tokens

# Testar mapeamento de trades
make test-trades

# Executar substream completo
make stream
```

#### 4. Empacotar
```bash
make pack
```

#### 5. Deploy para The Graph
```bash
# Para Graph Studio
graph deploy --studio ibi-cash-amm --deploy-key <YOUR_DEPLOY_KEY>

# Para Hosted Service
graph deploy --product hosted-service <GITHUB_USER>/ibi-cash-amm --deploy-key <YOUR_DEPLOY_KEY>
```

## 📊 Queries Úteis

### Tokens Mais Ativos
```graphql
query TopTokensByVolume {
  tokens(
    orderBy: totalVolumeSol
    orderDirection: desc
    first: 10
  ) {
    id
    name
    symbol
    currentPrice
    totalVolumeSol
    totalTrades
    bondingCurve {
      progressPercentage
      complete
    }
  }
}
```

### Histórico de Trades
```graphql
query TokenTrades($tokenId: ID!) {
  trades(
    where: { token: $tokenId }
    orderBy: blockTime
    orderDirection: desc
    first: 100
  ) {
    id
    tradeType
    trader
    solAmount
    tokenAmount
    pricePerToken
    blockTime
  }
}
```

## 🔧 Comandos Úteis
```bash
# Verificar código
make check

# Formatar código
make fmt

# Executar linter
make clippy

# Limpar build
make clean

# Ver ajuda
make help
```

## 📄 Licença

Este projeto está licenciado sob a MIT License.

```bash
substreams build
substreams auth
substreams gui       			  # Get streaming!
```

Optionally, you can publish your Substreams to the [Substreams Registry](https://substreams.dev).

```bash
substreams registry login         # Login to substreams.dev
substreams registry publish       # Publish your Substreams to substreams.dev
```

## Modules

### `map_my_data`

This module will extract accounts from the Pump.Fun program from the 'blocks_without_votes' foundational module
